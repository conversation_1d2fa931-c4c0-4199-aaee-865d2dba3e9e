"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/logbook/forms/tasking.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Tasking; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./vessel-rescue-fields */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\");\n/* harmony import */ var _person_rescue_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./person-rescue-field */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue-field.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _risk_analysis__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _barrel_optimize_names_SquareArrowOutUpRight_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=SquareArrowOutUpRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square-arrow-out-up-right.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons/SealogsFuelIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsFuelIcon.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/fuelTank */ \"(app-pages-browser)/./src/app/offline/models/fuelTank.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/fuelLog */ \"(app-pages-browser)/./src/app/offline/models/fuelLog.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Combobox is already imported from '@/components/ui/comboBox'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Tasking(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, inLogbook = false, previousDropEvent, vessel, members, locked, offline = false, fuelLogs } = param;\n    var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents, _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1, _currentTrip_tripEvents_nodes_find2, _currentTrip_tripEvents2, _currentTrip_tripEvents_nodes_find3, _currentTrip_tripEvents3, _currentTrip_tripEvents_nodes_find4, _currentTrip_tripEvents4, _currentTrip_tripEvents_nodes_find5, _currentTrip_tripEvents5;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\"));\n    const [tasking, setTasking] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openRiskAnalysis, setOpenRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [pauseGroup, setPauseGroup] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openTaskID, setOpenTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [completedTaskID, setCompletedTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [towingChecklistID, setTowingChecklistID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [groupID, setGroupID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentIncident, setCurrentIncident] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [taskingPausedValue, setTaskingPausedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingResumedValue, setTaskingResumedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingCompleteValue, setTaskingCompleteValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [locationDescription, setLocationDescription] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // const [members, setMembers] = useState<any>(false)\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankList, setFuelTankList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankBuffer, setFuelTankBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [updatedFuelLogs, setUpdatedFuelLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const fuelTankModel = new _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__[\"default\"]();\n    const fuelLogModel = new _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryGetFuelTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELTANKS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readFuelTanks.nodes;\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelTanks error\", error);\n        }\n    });\n    const getFuelTanks = async (fuelTankIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelTankIds);\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        } else {\n            await queryGetFuelTanks({\n                variables: {\n                    id: fuelTankIds\n                }\n            });\n        }\n    };\n    const handleSetVessel = (vessel)=>{\n        var _vessel_parentComponent_Components;\n        const fuelTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components === void 0 ? void 0 : _vessel_parentComponent_Components.nodes.filter((item)=>item.basicComponent.componentCategory === \"FuelTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (vessel) {\n            handleSetVessel(vessel);\n        }\n    }, [\n        vessel\n    ]);\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"HH:mm\"));\n    };\n    const offlineGetPreviousDropEvent = async ()=>{\n        const event = await tripEventModel.getById(previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id);\n        if (event) {\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n            if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                setCurrentLocation({\n                    latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                    longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        } else {\n            if ((previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id) > 0) {\n                if (offline) {\n                    offlineGetPreviousDropEvent();\n                } else {\n                    getPreviousDropEvent({\n                        variables: {\n                            id: previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const handleTaskingPauseChange = (selectedTask)=>{\n        setPauseGroup(selectedTask.value);\n        setTaskingPausedValue(selectedTask);\n    };\n    const [getPreviousDropEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n                setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n                if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                    var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                        longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                    });\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting previous event\", error);\n        }\n    });\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            var _currentTrip_tripEvents;\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                // eventType_TaskingID\n                if (!event.eventType_Tasking) {\n                    const eventType_Tasking = await taskingModel.getById(event.eventType_TaskingID);\n                    event.eventType_Tasking = eventType_Tasking;\n                }\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _currentTrip_tripEvents;\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations([\n                {\n                    label: \"--- Add new location ---\",\n                    value: \"newLocation\"\n                },\n                ...geoLocations.filter((location)=>location.title).map((location)=>({\n                        label: location.title,\n                        value: location.id,\n                        latitude: location.lat,\n                        longitude: location.long\n                    }))\n            ]);\n        }\n    }, [\n        geoLocations\n    ]);\n    const handleSave = async function() {\n        let vesselRescueID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, personRescueID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const variables = {\n            input: {\n                geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                time: time,\n                title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                type: type,\n                operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                vesselRescueID: vesselRescueID > 0 ? vesselRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.vesselRescueID,\n                personRescueID: personRescueID > 0 ? personRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.personRescueID,\n                currentEntryID: currentTrip.id,\n                tripEventID: tasking === null || tasking === void 0 ? void 0 : tasking.id,\n                pausedTaskID: +pauseGroup,\n                openTaskID: +openTaskID,\n                completedTaskID: +completedTaskID,\n                comments: content,\n                groupID: +groupID,\n                status: \"Open\",\n                cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : null,\n                sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : null\n            }\n        };\n        if (pauseGroup > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +pauseGroup,\n                    status: \"Paused\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +pauseGroup,\n                            status: \"Paused\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (openTaskID > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +openTaskID,\n                    status: \"Open\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +openTaskID,\n                            status: \"Open\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (completedTaskID > 0 && !currentEvent) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +completedTaskID > 0 ? +completedTaskID : (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    status: \"Completed\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +completedTaskID,\n                            status: \"Completed\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (currentEvent) {\n            if (offline) {\n                const data = await taskingModel.save({\n                    ...variables.input,\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                    tripEventID: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id)\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n                await getCurrentEvent(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                            ...variables.input\n                        }\n                    }\n                });\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            }\n        } else {\n            if (offline) {\n                const newID = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)();\n                await tripEventModel.save({\n                    id: newID,\n                    eventCategory: \"Tasking\",\n                    eventType_TaskingID: +newID,\n                    logBookEntrySectionID: currentTrip.id\n                });\n                const data = await taskingModel.save({\n                    id: +newID,\n                    geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                    time: time,\n                    title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                    fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                    type: type,\n                    operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                    lat: currentLocation.latitude.toString(),\n                    long: currentLocation.longitude.toString(),\n                    vesselRescueID: vesselRescueID,\n                    personRescueID: personRescueID,\n                    currentEntryID: currentTrip.id,\n                    pausedTaskID: +pauseGroup,\n                    openTaskID: +openTaskID,\n                    completedTaskID: +completedTaskID,\n                    comments: content,\n                    groupID: +groupID,\n                    status: \"Open\",\n                    cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                    sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false),\n                    tripEventID: newID\n                });\n                updateFuelLogs(+data.id);\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n            } else {\n                createEventType_Tasking({\n                    variables: {\n                        input: {\n                            geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                            time: time,\n                            title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                            fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                            type: type,\n                            operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                            lat: currentLocation.latitude.toString(),\n                            long: currentLocation.longitude.toString(),\n                            vesselRescueID: vesselRescueID,\n                            personRescueID: personRescueID,\n                            currentEntryID: currentTrip.id,\n                            pausedTaskID: +pauseGroup,\n                            openTaskID: +openTaskID,\n                            completedTaskID: +completedTaskID,\n                            comments: content,\n                            groupID: +groupID,\n                            status: \"Open\",\n                            cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                            sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false)\n                        }\n                    }\n                });\n            }\n        }\n        setCompletedTaskID(false);\n        setOpenTaskID(false);\n        setPauseGroup(false);\n    };\n    const [createEventType_Tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CreateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_Tasking;\n            updateFuelLogs(+data.id);\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: error.message\n            });\n        }\n    });\n    const [updateEventType_tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_tasking;\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating activity type tasking\", error);\n        }\n    });\n    const handleOperationTypeChange = (selectedOperation)=>{\n        if (selectedOperation.value === \"newLocation\") {\n            toast({\n                title: \"Getting your current location...\",\n                description: \"Please wait...\"\n            });\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    toast({\n                        title: \"Success\",\n                        description: \"Location found\"\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n        } else {\n            setTasking({\n                ...tasking,\n                operationType: selectedOperation.value\n            });\n        }\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            // Update tripEvent to clear location data\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    geoLocationID: 0,\n                    lat: null,\n                    long: null\n                }\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            toast({\n                title: \"Getting your current location...\",\n                description: \"Please wait...\"\n            });\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTasking({\n                ...tasking,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n            // Update tripEvent to reflect the selected location\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    geoLocationID: +value.value,\n                    lat: null,\n                    long: null\n                }\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n            // Update tripEvent to reflect the new coordinates so LocationField can display them\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    lat: value.latitude,\n                    long: value.longitude,\n                    geoLocationID: 0\n                }\n            });\n        }\n    };\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTasking({\n                    ...tasking,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTasking({\n                ...tasking,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Error creating GeoLocation\"\n            });\n            console.error(\"Error creating GeoLocation: \" + error.message);\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    const handleParentLocationChange = (selectedOption)=>{\n        if (selectedOption) {\n            setParentLocation(selectedOption.value);\n        } else {\n            setParentLocation(null);\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleTaskingGroupChange = (selectedGroup)=>{\n        setGroupID(selectedGroup.value);\n        setOpenTaskID(selectedGroup.value);\n        setTaskingResumedValue(selectedGroup);\n    };\n    const handleTaskingCompleteChange = (selectedGroup)=>{\n        setCompletedTaskID(selectedGroup.value);\n        setTaskingCompleteValue(selectedGroup);\n    };\n    const operationTypes = [\n        {\n            label: \"Vessel Mechanical / equipment failure\",\n            value: \"Vessel Mechanical or equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Vessel - other\",\n            value: \"Vessel - other\"\n        },\n        {\n            label: \"Person in water\",\n            value: \"Person in water\"\n        },\n        {\n            label: \"Person lost / missing\",\n            value: \"Person lost or missing\"\n        },\n        {\n            label: \"Suicide\",\n            value: \"Suicide\"\n        },\n        {\n            label: \"Medical condition\",\n            value: \"Medical condition\"\n        },\n        {\n            label: \"Person - other\",\n            value: \"Person - other\"\n        }\n    ];\n    const goSetTaskingTitle = (event)=>{\n        let title = \"\";\n        if (event && event.eventType_Tasking.type === \"TaskingStartUnderway\") {\n            title = event.eventType_Tasking.title;\n        }\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(tasking.title)) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(title))) {\n            setTasking({\n                ...tasking,\n                title: title\n            });\n        }\n    };\n    const findPreviousEvent = (selectedEvent)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            if (selectedEvent) {\n                if (selectedEvent.eventType_Tasking.vesselRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.vesselRescueID === selectedEvent.eventType_Tasking.vesselRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n                if (selectedEvent.eventType_Tasking.personRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.personRescueID === selectedEvent.eventType_Tasking.personRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n            }\n            goSetTaskingTitle(prevEvent);\n            return prevEvent;\n        }\n        if (type === \"TaskingComplete\") {\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents1;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            } else {\n                const res = prevEvent ? prevEvent : selectedEvent;\n                goSetTaskingTitle(res);\n                return res;\n            }\n        }\n        goSetTaskingTitle(selectedEvent);\n        return selectedEvent;\n    };\n    const findPreviousRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.vesselRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const findPreviousHumanRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.personRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const currentOperationTypeLabel = (label)=>{\n        return label ? label : \"-- Select operation type --\";\n    };\n    const currentOperationTypeValue = (value)=>{\n        return value;\n    };\n    const getPreviousSAROP = (sarop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"cgop\") {\n            return \"\";\n        }\n        if (currentIncident === \"sarop\") {\n            return sarop ? sarop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.sarop) ? e.eventType_Tasking.sarop : sarop ? sarop : \"\";\n    };\n    const getPreviousCGOP = (cgop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"sarop\") {\n            return \"\";\n        }\n        if (currentIncident === \"cgop\") {\n            return cgop ? cgop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.cgop) ? e.eventType_Tasking.cgop : cgop ? cgop : \"\";\n    };\n    const getIsSAROP = (sarop)=>{\n        var _tasking_cgop, _tasking_sarop;\n        return currentIncident === \"sarop\" || currentIncident !== \"sarop\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(sarop)) && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_cgop = tasking === null || tasking === void 0 ? void 0 : tasking.cgop) !== null && _tasking_cgop !== void 0 ? _tasking_cgop : \"\")) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_sarop = tasking === null || tasking === void 0 ? void 0 : tasking.sarop) !== null && _tasking_sarop !== void 0 ? _tasking_sarop : \"\"));\n    };\n    const getIsCGOP = (cgop)=>{\n        var _tasking_sarop, _tasking_cgop;\n        return currentIncident === \"cgop\" || currentIncident !== \"cgop\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(cgop)) && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_sarop = tasking === null || tasking === void 0 ? void 0 : tasking.sarop) !== null && _tasking_sarop !== void 0 ? _tasking_sarop : \"\")) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_cgop = tasking === null || tasking === void 0 ? void 0 : tasking.cgop) !== null && _tasking_cgop !== void 0 ? _tasking_cgop : \"\"));\n    };\n    const getPreviousFuelLevel = (fuelLevel)=>{\n        var _selectedEvent_eventType_Tasking, _currentTrip_tripEvents;\n        if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.fuelLevel) > 0) {\n            var _selectedEvent_eventType_Tasking1;\n            return selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking1.fuelLevel;\n        }\n        if (fuelLevel || (tasking === null || tasking === void 0 ? void 0 : tasking.updatedFuelLevel)) {\n            return fuelLevel;\n        }\n        const fuelLevels = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>event.eventType_Tasking.fuelLevel > 0).map((event)=>event.eventType_Tasking.fuelLevel);\n        const minFuelLevel = (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? fuelLevels[fuelLevels.length - 1] : fuelLevel;\n        return (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? minFuelLevel : fuelLevel ? fuelLevel : \"\";\n    };\n    const getPreviousTask = (task)=>{\n        var _prevEvent_eventType_Tasking, _prevEvent_eventType_Tasking1, _prevEvent_eventType_Tasking2;\n        if (task) {\n            return task;\n        }\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _prevEvent_eventType_Tasking3, _prevEvent_eventType_Tasking4, _prevEvent_eventType_Tasking5, _prevEvent_eventType_Tasking6;\n            setCompletedTaskID(prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking3 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking3 === void 0 ? void 0 : _prevEvent_eventType_Tasking3.id);\n            setTaskingCompleteValue({\n                label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking4 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking4 === void 0 ? void 0 : _prevEvent_eventType_Tasking4.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking5 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking5 === void 0 ? void 0 : _prevEvent_eventType_Tasking5.title),\n                value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking6 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking6 === void 0 ? void 0 : _prevEvent_eventType_Tasking6.id\n            });\n        }\n        return prevEvent ? {\n            label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking === void 0 ? void 0 : _prevEvent_eventType_Tasking.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking1 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking1 === void 0 ? void 0 : _prevEvent_eventType_Tasking1.title),\n            value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking2 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking2 === void 0 ? void 0 : _prevEvent_eventType_Tasking2.id\n        } : task;\n    };\n    const isVesselRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.vesselRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID) > 0;\n    };\n    const isPersonRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.personRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID) > 0;\n    };\n    const displayVessesRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isVesselRescue() || type === \"TaskingOnTow\" && isVesselRescue() || type === \"TaskingComplete\" && isVesselRescue() || tasking.operationType === \"Vessel Mechanical or equipment failure\" || tasking.operationType === \"Vessel adrift\" || tasking.operationType === \"Vessel aground\" || tasking.operationType === \"Capsize\" || tasking.operationType === \"Vessel requiring tow\" || tasking.operationType === \"Flare sighting\" || tasking.operationType === \"Vessel sinking\" || tasking.operationType === \"Collision\" || tasking.operationType === \"Vessel overdue\" || tasking.operationType === \"Vessel - other\" || +tasking.lat > 0 && +tasking.long > 0) {\n            return true;\n        }\n        return false;\n    };\n    const displayPersonRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isPersonRescue() || type === \"TaskingOnTow\" && isPersonRescue() || type === \"TaskingComplete\" && isPersonRescue() || tasking.operationType === \"Person in water\" || tasking.operationType === \"Person lost or missing\" || tasking.operationType === \"Suicide\" || tasking.operationType === \"Medical condition\" || tasking.operationType === \"Person - other\") {\n            return true;\n        }\n        return false;\n    };\n    const handleSaropChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"sarop\");\n        }\n    };\n    const handleCgopChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"cgop\");\n        }\n    };\n    const handleUpdateFuelTank = (tank, value)=>{\n        if (tank.capacity < +value) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Fuel level cannot be higher than tank capacity of \" + tank.capacity\n            });\n            return;\n        }\n        setFuelTankList(fuelTankList.map((item)=>{\n            if (item.id === tank.id) {\n                return {\n                    ...item,\n                    currentLevel: +value\n                };\n            }\n            return item;\n        }));\n        setTasking({\n            ...tasking,\n            fuelLog: false\n        });\n        if (fuelTankBuffer.length > 0 && fuelTankBuffer.filter((item)=>item.tankID === tank.id)) {\n            setFuelTankBuffer(fuelTankBuffer.map((item)=>{\n                if (item.tankID === tank.id) {\n                    return {\n                        ...item,\n                        value: +value\n                    };\n                }\n                return item;\n            }));\n        } else {\n            setFuelTankBuffer([\n                ...fuelTankBuffer,\n                {\n                    tankID: tank.id,\n                    value: +value\n                }\n            ]);\n        }\n    };\n    // Create a debounced version of the update function\n    // const handleUpdateFuelTank = useCallback(\n    //     debounce((tank: any, value: any) => {\n    //         updateFuelTankValue(tank, value)\n    //     }, 500), // 500ms delay\n    //     [fuelTankList, tasking],\n    // )\n    const [updateFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UPDATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel log\", error);\n        }\n    });\n    const [createFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.createFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error creating fuel log\", error);\n        }\n    });\n    const [updateFuelTank] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateFuelTank, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelTank;\n            const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === data.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n            if (fuelLog) {\n                updateFuelLog({\n                    variables: {\n                        input: {\n                            id: fuelLog.id,\n                            fuelAfter: +fuelLog.fuelAfter\n                        }\n                    }\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel tank\", error);\n        }\n    });\n    const updateFuelLogs = function() {\n        let currentID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        if (fuelTankList) {\n            Promise.all(fuelTankList === null || fuelTankList === void 0 ? void 0 : fuelTankList.map(async (fuelTank)=>{\n                const variables = {\n                    input: {\n                        id: fuelTank.id,\n                        currentLevel: fuelTank.currentLevel\n                    }\n                };\n                if (!currentEvent) {\n                    if (offline) {\n                        await fuelTankModel.save({\n                            id: fuelTank.id,\n                            currentLevel: fuelTank.currentLevel\n                        });\n                    } else {\n                        updateFuelTank({\n                            variables: variables\n                        });\n                    }\n                }\n                if (currentEvent) {\n                    if (offline) {\n                        var _currentEvent_eventType_Tasking_fuelLog_nodes_find;\n                        await fuelLogModel.save({\n                            id: ((_currentEvent_eventType_Tasking_fuelLog_nodes_find = currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>{\n                                var _log_fuelTank;\n                                ((_log_fuelTank = log.fuelTank) === null || _log_fuelTank === void 0 ? void 0 : _log_fuelTank.id) === fuelTank.id;\n                            })) === null || _currentEvent_eventType_Tasking_fuelLog_nodes_find === void 0 ? void 0 : _currentEvent_eventType_Tasking_fuelLog_nodes_find.id) || (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        updateFuelLog({\n                            variables: {\n                                input: {\n                                    id: currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>log.fuelTank.id === fuelTank.id).id,\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                } else {\n                    if (offline) {\n                        await fuelLogModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        createFuelLog({\n                            variables: {\n                                input: {\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                }\n            }));\n        }\n    };\n    const getInitialFuelLevel = (tank)=>{\n        if (fuelTankBuffer.length > 0) {\n            const fuelTank = fuelTankBuffer.find((item)=>item.tankID === tank.id);\n            if (fuelTank) {\n                return fuelTank.value;\n            }\n        }\n        if (tripReport.length > 0) {\n            var _fuelLogs_filter_sort;\n            const fuelLogs = tripReport.map((trip)=>{\n                return trip.tripEvents.nodes.filter((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes.length > 0 || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes.length > 0 || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes.length > 0).flatMap((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes);\n            }).flat();\n            const lastFuelLog = fuelLogs === null || fuelLogs === void 0 ? void 0 : (_fuelLogs_filter_sort = fuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>b.id - a.id)) === null || _fuelLogs_filter_sort === void 0 ? void 0 : _fuelLogs_filter_sort[0];\n            if (lastFuelLog) {\n                return lastFuelLog.fuelAfter;\n            }\n        }\n        // if (\n        //     currentTrip &&\n        //     currentTrip?.tripEvents?.nodes?.length > 0 &&\n        //     currentTrip.tripEvents.nodes.find(\n        //         (event: any) =>\n        //             event.eventCategory === 'Tasking' &&\n        //             event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //     )\n        // ) {\n        //     const fuelLog = currentTrip.tripEvents.nodes\n        //         .filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (fuelLog) {\n        //         return fuelLog.fuelAfter\n        //     }\n        // }\n        // if (tripReport && tripReport.length > 1) {\n        //     const latestTripFuelLog = tripReport\n        //         .filter((trip: any) => trip.id < currentTrip.id)\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.tripEvents?.nodes.filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (latestTripFuelLog) {\n        //         return latestTripFuelLog.fuelAfter\n        //     }\n        // }\n        const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n        return fuelLog ? +tank.capacity > +fuelLog.fuelAfter ? +fuelLog.fuelAfter : +tank.capacity : +tank.currentLevel;\n    };\n    const getFuelLogs = async (fuelLogIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelLogIds);\n        } else {\n            await queryGetFuelLogs({\n                variables: {\n                    id: fuelLogIds\n                }\n            });\n        }\n    };\n    const [queryGetFuelLogs] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELLOGS, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.readFuelLogs.nodes;\n            setUpdatedFuelLogs(data);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelLogs error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFuelLogs(fuelLogs.map((item)=>item.id));\n    }, []);\n    var _getPreviousCGOP, _getPreviousSAROP;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !inLogbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 text-sm font-semibold uppercase\",\n                        children: [\n                            type === \"TaskingStartUnderway\" && \"Tasking start / underway\",\n                            type === \"TaskingPaused\" && \"Tasking paused\",\n                            type === \"TaskingResumed\" && \"Tasking resumed\",\n                            type === \"TaskingOnScene\" && tasking.title,\n                            type === \"TaskingOnTow\" && tasking.title,\n                            type === \"TaskingComplete\" && tasking.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1680,\n                        columnNumber: 31\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem] mb-2\",\n                        children: [\n                            \"Give this tasking a title and choose an operation type.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1691,\n                                columnNumber: 21\n                            }, this),\n                            \"Recording fuel levels goes toward\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"fuel reports for allocating to different operations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1693,\n                                columnNumber: 21\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1689,\n                        columnNumber: 17\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem]\",\n                        children: \"Utilise attached checklist to ensure towing procedure is followed and any risks identified.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1699,\n                        columnNumber: 21\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_29__.CheckFieldLabel, {\n                        type: \"checkbox\",\n                        checked: allChecked,\n                        className: \"w-fit\",\n                        variant: \"success\",\n                        rightContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SquareArrowOutUpRight_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 1711,\n                            columnNumber: 29\n                        }, void 0),\n                        onClick: ()=>{\n                            setOpenRiskAnalysis(true);\n                        },\n                        label: \"Towing checklist - risk analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1705,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Time when tasking takes place\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1722,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        time: time,\n                                        handleTimeChange: handleTimeChange,\n                                        timeID: \"time\",\n                                        fieldName: \"Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1723,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1720,\n                                columnNumber: 21\n                            }, this),\n                            type === \"TaskingStartUnderway\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Title of tasking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1733,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                        id: \"title\",\n                                        name: \"title\",\n                                        type: \"text\",\n                                        value: (tasking === null || tasking === void 0 ? void 0 : tasking.title) ? tasking.title : \"\",\n                                        placeholder: \"Title\",\n                                        onChange: (e)=>{\n                                            setTasking({\n                                                ...tasking,\n                                                title: e.target.value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1734,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1731,\n                                columnNumber: 25\n                            }, this),\n                            fuelTankList && fuelTankList.map((tank)=>/*#__PURE__*/ {\n                                var _tasking_fuelLog_find;\n                                var _tank_currentLevel, _ref;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" flex flex-col gap-2 my-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__.SealogsFuelIcon, {\n                                                    className: \"size-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1755,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                    children: tank.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1756,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1754,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                            type: \"number\",\n                                            placeholder: \"Fuel end\",\n                                            value: (_ref = (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : (tasking === null || tasking === void 0 ? void 0 : tasking.fuelLog) ? (_tasking_fuelLog_find = tasking.fuelLog.find((log)=>+log.fuelTank.id === +tank.id)) === null || _tasking_fuelLog_find === void 0 ? void 0 : _tasking_fuelLog_find.fuelAfter : getInitialFuelLevel(tank)) !== null && _ref !== void 0 ? _ref : 0,\n                                            min: 0,\n                                            max: tank.capacity,\n                                            onChange: (e)=>handleUpdateFuelTank(tank, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1758,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, tank.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 1751,\n                                    columnNumber: 29\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2 my-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                children: \"Location where tasking takes place\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1785,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                offline: offline,\n                                                setCurrentLocation: setCurrentLocation,\n                                                handleLocationChange: handleLocationChange,\n                                                currentEvent: tripEvent.eventType_Tasking\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1786,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1784,\n                                        columnNumber: 25\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                            id: \"location-description\",\n                                            rows: 4,\n                                            placeholder: \"Location description\",\n                                            value: locationDescription !== null && locationDescription !== void 0 ? locationDescription : \"\",\n                                            onChange: (e)=>{\n                                                setLocationDescription(e.target.value);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1797,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1796,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1783,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    type === \"TaskingPaused\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                                    options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Open\").map((group)=>({\n                                                            value: group.eventType_Tasking.id,\n                                                            label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                        })) : [],\n                                                    value: tasking.pausedTaskID > 0 ? {\n                                                        value: tasking.pausedTaskID,\n                                                        label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.title)\n                                                    } : taskingPausedValue,\n                                                    onChange: handleTaskingPauseChange,\n                                                    placeholder: \"Select Task to pause\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1815,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1814,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: (selectedEvent && content || !selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    id: \"comment\",\n                                                    placeholder: \"Comment\",\n                                                    className: \"w-full\",\n                                                    content: content,\n                                                    handleEditorChange: handleEditorChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1863,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1860,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status !== \"Completed\").map((group)=>({\n                                                    value: group.eventType_Tasking.id,\n                                                    label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                })) : [],\n                                            value: tasking.completedTaskID > 0 ? {\n                                                value: tasking.completedTaskID,\n                                                label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find2 = _currentTrip_tripEvents2.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find2 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find2.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find3 = _currentTrip_tripEvents3.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find3 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find3.eventType_Tasking.title)\n                                            } : getPreviousTask(taskingCompleteValue),\n                                            onChange: handleTaskingCompleteChange,\n                                            placeholder: \"Select Task to Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1878,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1877,\n                                        columnNumber: 29\n                                    }, this),\n                                    type === \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                        options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Paused\").map((group)=>({\n                                                value: group.eventType_Tasking.id,\n                                                label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                            })) : [],\n                                        value: tasking.openTaskID > 0 ? {\n                                            value: tasking.openTaskID,\n                                            label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents4 = currentTrip.tripEvents) === null || _currentTrip_tripEvents4 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find4 = _currentTrip_tripEvents4.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find4 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find4.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents5 = currentTrip.tripEvents) === null || _currentTrip_tripEvents5 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find5 = _currentTrip_tripEvents5.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find5 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find5.eventType_Tasking.title)\n                                        } : taskingResumedValue,\n                                        onChange: handleTaskingGroupChange,\n                                        placeholder: \"Select Task to continue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1924,\n                                        columnNumber: 29\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && type !== \"TaskingComplete\" && type !== \"TaskingOnTow\" && type !== \"TaskingOnScene\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: operationTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: operationTypes.map((type)=>({\n                                                    value: type.value,\n                                                    label: type.label\n                                                })),\n                                            value: {\n                                                value: currentOperationTypeValue(tasking === null || tasking === void 0 ? void 0 : tasking.operationType),\n                                                label: currentOperationTypeLabel(tasking === null || tasking === void 0 ? void 0 : tasking.operationType)\n                                            },\n                                            onChange: handleOperationTypeChange,\n                                            placeholder: \"Operation type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1971,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1811,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                                className: \"max-w-[40rem]\",\n                                children: [\n                                    \"Everything else below this section is\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"optional can be completed later\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1995,\n                                        columnNumber: 25\n                                    }, this),\n                                    \". However, all the details loaded here will be used for any tasking reports required.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1993,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1719,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                locationDescription: locationDescription,\n                setLocationDescription: setLocationDescription,\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousRescueID(tasking.vesselRescueID),\n                type: type,\n                eventCurrentLocation: {\n                    currentLocation: currentLocation,\n                    geoLocationID: tasking.geoLocationID\n                },\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2004,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayPersonRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_person_rescue_field__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousHumanRescueID(tasking.personRescueID),\n                type: type,\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2028,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 text-sm font-semibold uppercase\",\n                        children: \"Incident type / number\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2045,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem]\",\n                        children: \"Detail if incident was tasked by Police, RCCNZ or Coastguard and associated incident number if applicable\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2048,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full items-start  grid-cols-1 md:grid-cols-5 md:grid-rows-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" mt-4 md:my-4 w-full flex items-center space-x-2 py-3\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                        id: \"task-cgop\",\n                                        checked: getIsCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop),\n                                        onCheckedChange: (checked)=>{\n                                            if (checked) handleCgopChange({\n                                                target: {\n                                                    value: \"on\"\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2055,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        htmlFor: \"task-cgop\",\n                                        //className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 uppercase\"\n                                        className: \"!mb-0\",\n                                        children: \"CoastGuard Rescue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2065,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2053,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                    id: \"cgop\",\n                                    type: \"text\",\n                                    onChange: (e)=>{\n                                        setTasking({\n                                            ...tasking,\n                                            sarop: \"\",\n                                            cgop: e.target.value\n                                        }), setCurrentIncident(\"cgop\");\n                                    },\n                                    value: (_getPreviousCGOP = getPreviousCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop)) !== null && _getPreviousCGOP !== void 0 ? _getPreviousCGOP : \"\",\n                                    \"aria-describedby\": \"cgop-error\",\n                                    required: true,\n                                    placeholder: \"Police / RCCNZ number\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 2074,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2072,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" mt-4 md:my-4 w-full flex items-center space-x-2 py-3\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                        id: \"task-sarop\",\n                                        checked: getIsSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop),\n                                        onCheckedChange: (checked)=>{\n                                            if (checked) handleSaropChange({\n                                                target: {\n                                                    value: \"on\"\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2093,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        htmlFor: \"task-sarop\",\n                                        className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 uppercase !mb-0\",\n                                        children: \"SAROP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2103,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2091,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                    id: \"sarop\",\n                                    type: \"text\",\n                                    onChange: (e)=>{\n                                        setTasking({\n                                            ...tasking,\n                                            sarop: e.target.value,\n                                            cgop: \"\"\n                                        }), setCurrentIncident(\"sarop\");\n                                    },\n                                    value: (_getPreviousSAROP = getPreviousSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop)) !== null && _getPreviousSAROP !== void 0 ? _getPreviousSAROP : \"\",\n                                    \"aria-describedby\": \"sarop-error\",\n                                    required: true,\n                                    placeholder: \"Police / RCCNZ number\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 2111,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2109,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2052,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_31__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2133,\n                        columnNumber: 17\n                    }, this),\n                    !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_31__.Button, {\n                        onClick: ()=>handleSave(0, 0),\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2139,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2132,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2152,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2151,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                            id: \"parent-location\",\n                            options: locations || [],\n                            onChange: handleParentLocationChange,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2161,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2160,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2170,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2169,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2180,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2179,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2145,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_risk_analysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                offline: offline,\n                selectedEvent: selectedEvent,\n                crewMembers: members,\n                towingChecklistID: towingChecklistID,\n                setTowingChecklistID: setTowingChecklistID,\n                setAllChecked: setAllChecked,\n                onSidebarClose: ()=>setOpenRiskAnalysis(false),\n                logBookConfig: undefined,\n                currentTrip: currentTrip,\n                open: openRiskAnalysis,\n                onOpenChange: setOpenRiskAnalysis\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2191,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n        lineNumber: 1678,\n        columnNumber: 9\n    }, this);\n}\n_s(Tasking, \"FkSGTOI8nWEa7r9P1zE0J+7ZiEs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery\n    ];\n});\n_c = Tasking;\nvar _c;\n$RefreshReg$(_c, \"Tasking\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\n"));

/***/ })

});