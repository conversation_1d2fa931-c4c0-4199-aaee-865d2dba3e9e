'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import {
    <PERSON><PERSON>,
    DialogContent,
    <PERSON><PERSON>Header,
    <PERSON>alogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { CalendarIcon, X, Check, type LucideIcon } from 'lucide-react'
import { TimePicker } from '@/components/ui/time-picker'
import { cn } from '../app/lib/utils'
import { format, isAfter, isBefore, set } from 'date-fns'
import type { DateRange } from 'react-day-picker'
import { Label, LabelPosition } from './ui/label'
import { Separator } from './ui/separator'
import { useMediaQuery } from '@reactuses/core'
import { useBreakpoints } from './hooks/useBreakpoints'
import { P } from './ui'
// Select components removed in favor of TimePicker

type DatePickerMode = 'range' | 'single'
type TimePickerMode = 'single' | 'range'
type DatePickerType = 'date' | 'datetime'

// Define icon type that can be either a React node or a Lucide icon component
type IconType = React.ReactNode | LucideIcon

interface DateValidation {
    minDate?: Date
    maxDate?: Date
    disabledDates?: Date[]
    disabledDaysOfWeek?: number[] // 0 = Sunday, 6 = Saturday
}

interface DatePickerProps
    extends Omit<
        React.ButtonHTMLAttributes<HTMLButtonElement>,
        'value' | 'onChange' | 'type'
    > {
    onChange: (value: any) => void
    className?: string
    placeholder?: string
    /**
     * Mode of the date picker.
     * 'range' for date range selection (default)
     * 'single' for single date selection
     */
    mode?: DatePickerMode
    /**
     * Type of the date picker.
     * 'date' for date only selection (default)
     * 'datetime' for date and time selection
     */
    type?: DatePickerType
    disabled?: boolean
    value?: Date | DateRange
    /**
     * Date format for display
     * @default 'LLL dd, y'
     */
    dateFormat?: string
    /** Validation rules for date selection */
    validation?: DateValidation
    /** Number of months to display in the calendar */
    numberOfMonths?: number
    /** Whether to close the popover when a date is selected (single mode only) */
    closeOnSelect?: boolean
    /** Whether to show week numbers */
    showWeekNumbers?: boolean
    /** Whether to include time picker (deprecated, use type='datetime' instead) */
    includeTime?: boolean
    /** Mode of the time picker: 'single' or 'range' */
    timeMode?: TimePickerMode
    /** Time format for display */
    timeFormat?: string
    /** Interval between time options in minutes */
    timeInterval?: number
    /** Optional label for the date picker */
    label?: string
    /** Position of the label relative to the date picker */
    labelPosition?: LabelPosition
    /** Whether to show a clear button inside the date picker */
    clearable?: boolean
    /**
     * Custom icon to display in the date picker button.
     * Can be a Lucide icon component (CalendarIcon) or a JSX element (<CalendarIcon />)
     */
    icon?: IconType
    /** Whether to show a confirmation button after selecting a date/time */
    confirmSelection?: boolean
    /** Text to display on the confirmation button */
    confirmButtonText?: string
    /** Whether to use a modal for the date picker */
    modal?: boolean
    wrapperClassName?: string
}

const DatePicker = ({
    onChange,
    className,
    placeholder = 'Select date',
    mode = 'range',
    type = 'date',
    disabled = false,
    value,
    dateFormat = 'dd LLLL, y',
    validation,
    numberOfMonths = mode === 'range' ? 2 : 1,
    closeOnSelect = true,
    showWeekNumbers = false,
    includeTime = false, // deprecated, use type='datetime' instead
    timeMode = 'single',
    timeFormat = 'HH:mm',
    timeInterval = 30,
    label,
    labelPosition = 'top',
    clearable = false,
    icon,
    confirmSelection = true,
    confirmButtonText = 'Confirm',
    modal = false,
    wrapperClassName = '',
    ...buttonProps
}: DatePickerProps) => {
    const [dateValue, setDateValue] = useState<DateRange | Date | undefined>(
        value ||
            (mode === 'range' ? { from: undefined, to: undefined } : undefined),
    )

    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component
    const [open, setOpen] = useState(false)
    const [time, setTime] = useState<{ hour: number; minute: number } | null>(
        null,
    )
    const [toTime, setToTime] = useState<{
        hour: number
        minute: number
    } | null>(null)

    // State to track pending selection when confirmation button is enabled
    const [pendingSelection, setPendingSelection] = useState<
        DateRange | Date | undefined
    >(value || undefined)

    // State to track the current month being displayed in the calendar
    const [currentMonth, setCurrentMonth] = useState<Date>(() => {
        if (value instanceof Date) {
            return value
        } else if (value && 'from' in value && value.from) {
            return value.from
        }
        return new Date()
    })

    // Set placeholder based on mode
    const actualPlaceholder =
        mode === 'range' ? 'Select date range' : 'Select date'

    // Optimized deep equality check for dates
    const isDateEqual = (a: Date | undefined, b: Date | undefined): boolean => {
        if (a === b) return true
        if (!a || !b) return false
        return (
            a instanceof Date &&
            b instanceof Date &&
            a.getTime() === b.getTime()
        )
    }

    const isValueEqual = (a: any, b: any): boolean => {
        if (a === b) return true
        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b)
        if (
            a &&
            b &&
            typeof a === 'object' &&
            typeof b === 'object' &&
            'from' in a &&
            'to' in a &&
            'from' in b &&
            'to' in b
        ) {
            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to)
        }
        return false
    }

    // Helper functions for time initialization
    const getCurrentTime = () => {
        const now = new Date()
        return { hour: now.getHours(), minute: now.getMinutes() }
    }

    const getTimeFromDate = (date: Date) => ({
        hour: date.getHours(),
        minute: date.getMinutes(),
    })

    const initializeTime = (
        existingTime: { hour: number; minute: number } | null,
        date?: Date,
    ) => {
        if (existingTime) return existingTime
        if (date && (date.getHours() || date.getMinutes()))
            return getTimeFromDate(date)
        return shouldIncludeTime ? getCurrentTime() : null
    }

    // Helper function to reset date value based on mode
    const getEmptyDateValue = () =>
        mode === 'range' ? { from: undefined, to: undefined } : undefined

    // Helper function to clear all date and time state
    const clearAllState = () => {
        setDateValue(getEmptyDateValue())
        setPendingSelection(undefined)
        setTime(null)
        setToTime(null)
        onChange(null)
    }

    // Helper function to render icon consistently
    const renderIcon = (className: string = 'text-muted-foreground') => {
        if (!icon)
            return (
                <CalendarIcon
                    size={20}
                    className={cn('text-neutral-400', className)}
                />
            )

        if (React.isValidElement(icon)) {
            return React.cloneElement(icon as React.ReactElement, {
                className: cn('w-5 h-5', className),
            })
        }

        return React.createElement(icon as any, {
            size: 20,
            className: cn(className),
        })
    }

    // Helper function to get time picker value - simplified to use pendingSelection directly
    const getTimePickerValue = (pendingDate?: Date) => {
        if (pendingDate && !isNaN(pendingDate.getTime())) {
            return pendingDate
        }
        // Default to current time if no pending date
        const now = new Date()
        return now
    }

    // Helper function to extract time from a date
    const extractTimeFromDate = (date: Date) => {
        return {
            hour: date.getHours(),
            minute: date.getMinutes(),
        }
    }

    // Helper function to get preserved time for date changes
    const getPreservedTime = (isToTime = false) => {
        // First, try to get time from current pendingSelection
        if (pendingSelection) {
            if (mode === 'range') {
                const range = pendingSelection as DateRange
                const sourceDate = isToTime ? range.to : range.from
                if (
                    sourceDate &&
                    (sourceDate.getHours() !== 0 ||
                        sourceDate.getMinutes() !== 0)
                ) {
                    return extractTimeFromDate(sourceDate)
                }
            } else if (pendingSelection instanceof Date) {
                if (
                    pendingSelection.getHours() !== 0 ||
                    pendingSelection.getMinutes() !== 0
                ) {
                    return extractTimeFromDate(pendingSelection)
                }
            }
        }

        // Fallback to current time state
        const currentTimeState = isToTime ? toTime : time
        if (currentTimeState) {
            return currentTimeState
        }

        // Final fallback to current time
        const now = new Date()
        return extractTimeFromDate(now)
    }

    useEffect(() => {
        // Only update if the value has actually changed
        if (!isValueEqual(value, dateValue)) {
            if (value) {
                setDateValue(value)
                // Also update pendingSelection with the value
                setPendingSelection(value)

                // Update currentMonth to show the month of the selected date
                if (value instanceof Date) {
                    setCurrentMonth(value)
                    const timeFromDate = initializeTime(null, value)
                    if (timeFromDate) setTime(timeFromDate)
                } else if ('from' in value && value.from instanceof Date) {
                    const { from, to } = value
                    setCurrentMonth(from)
                    const fromTime = initializeTime(null, from)
                    if (fromTime) setTime(fromTime)
                    if (to instanceof Date) {
                        const toTimeFromDate = initializeTime(null, to)
                        if (toTimeFromDate) setToTime(toTimeFromDate)
                    }
                }
            } else {
                // When value is null/undefined, reset to initial state but maintain format
                setDateValue(getEmptyDateValue())
                setPendingSelection(undefined)
                // Keep currentMonth as is when clearing, don't reset to current date
            }
        }
    }, [value, mode])

    const validateDate = (date: Date): boolean => {
        if (!validation) return true
        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } =
            validation
        if (minDate && isBefore(date, minDate)) return false
        if (maxDate && isAfter(date, maxDate)) return false
        if (
            disabledDates?.some(
                (disabledDate) =>
                    format(disabledDate, 'yyyy-MM-dd') ===
                    format(date, 'yyyy-MM-dd'),
            )
        )
            return false
        if (disabledDaysOfWeek?.includes(date.getDay())) return false
        return true
    }

    const applyTime = (
        date: Date | undefined,
        t: { hour: number; minute: number } | null,
    ) =>
        date && t
            ? set(date, {
                  hours: t.hour,
                  minutes: t.minute,
                  seconds: 0,
                  milliseconds: 0,
              })
            : date

    const handleValueChange = (newValue: DateRange | Date | undefined) => {
        console.log('DateRange handleValueChange:', {
            newValue,
            pendingSelection,
            shouldIncludeTime,
        })

        if (!newValue) {
            // When a date is unselected, maintain the format consistency
            setDateValue(getEmptyDateValue())
            setPendingSelection(undefined)
            onChange(null)
            return
        }

        if (mode === 'range') {
            const { from, to } = newValue as DateRange
            if (from && !validateDate(from)) return
            if (to && !validateDate(to)) return

            // If confirmation is required, store the selection in pending state with preserved time
            if (confirmSelection) {
                let preservedFrom = from
                let preservedTo = to

                // Preserve existing time when setting pending selection
                if (from && shouldIncludeTime) {
                    const preservedTime = getPreservedTime(false)
                    const fromWithTime = applyTime(from, preservedTime)
                    if (fromWithTime) {
                        preservedFrom = fromWithTime
                    }
                }
                if (to && shouldIncludeTime) {
                    const preservedToTime = getPreservedTime(true)
                    const toWithTime = applyTime(to, preservedToTime)
                    if (toWithTime) {
                        preservedTo = toWithTime
                    }
                }

                setPendingSelection({ from: preservedFrom, to: preservedTo })
            } else {
                finalizeSelection({ from, to })
            }
        } else {
            const singleDate = newValue as Date
            if (!validateDate(singleDate)) return

            // If confirmation is required, store the selection in pending state with preserved time
            if (confirmSelection) {
                let preservedDate = singleDate

                // Preserve existing time when setting pending selection
                if (shouldIncludeTime) {
                    const preservedTime = getPreservedTime(false)
                    const dateWithTime = applyTime(singleDate, preservedTime)
                    if (dateWithTime) {
                        preservedDate = dateWithTime
                    }
                }

                setPendingSelection(preservedDate)
            } else {
                finalizeSelection(
                    singleDate,
                    closeOnSelect && !shouldIncludeTime,
                )
            }
        }
    }

    const handleTimeChange = (date: Date, isToTime = false) => {
        if (!date) return

        console.log('DateRange handleTimeChange:', {
            date,
            isToTime,
            pendingSelection,
        })

        // Update pendingSelection directly with the new time
        if (confirmSelection) {
            if (mode === 'range') {
                if (isToTime && (pendingSelection as DateRange)?.to) {
                    setPendingSelection({
                        ...(pendingSelection as DateRange),
                        to: date,
                    })
                } else if (!isToTime && (pendingSelection as DateRange)?.from) {
                    setPendingSelection({
                        ...(pendingSelection as DateRange),
                        from: date,
                    })
                }
            } else if (pendingSelection instanceof Date) {
                setPendingSelection(date)
            }
        }

        // Update the time state for display purposes
        const newTime = { hour: date.getHours(), minute: date.getMinutes() }
        if (isToTime) {
            setToTime(newTime)
        } else {
            setTime(newTime)
        }

        // If confirmation is not required, call onChange directly
        if (!confirmSelection) {
            if (mode === 'range') {
                if (isToTime && (dateValue as DateRange)?.to) {
                    onChange({
                        startDate: (dateValue as DateRange).from,
                        endDate: date,
                    })
                } else if (!isToTime && (dateValue as DateRange)?.from) {
                    onChange({
                        startDate: date,
                        endDate: (dateValue as DateRange).to,
                    })
                }
            } else {
                onChange(date)
            }
        }
    }

    const handleClear = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation() // Prevent triggering the popover
        clearAllState()
    }

    // Function to handle clear button click inside the popover
    const handleClearInPopover = () => {
        clearAllState()
        setOpen(false) // Close the popover after clearing
    }

    // Helper function to finalize selection (used by both direct selection and confirmation)
    const finalizeSelection = (
        selection: DateRange | Date,
        closePopover = false,
    ) => {
        if (mode === 'range') {
            const { from, to } = selection as DateRange
            setDateValue({ from, to })

            // When confirming with time picker, the selection already contains the correct time
            // When not confirming, preserve existing time state
            const rangeResult = {
                startDate: from,
                endDate: to,
            }
            console.log(
                'DateRange finalizeSelection - range mode result:',
                rangeResult,
            )
            onChange(rangeResult)
        } else {
            const singleDate = selection as Date
            setDateValue(singleDate)

            // When confirming with time picker, the selection already contains the correct time
            onChange(singleDate)
        }

        if (closePopover) {
            setOpen(false)
            setPendingSelection(undefined)
        }
    }

    // Function to handle confirmation button click
    const handleConfirm = () => {
        if (!pendingSelection) return
        console.log('DateRange handleConfirm:', {
            pendingSelection,
            time,
            toTime,
        })
        finalizeSelection(pendingSelection, true)
    }

    // Function to handle month navigation (for dropdowns and arrow buttons)
    const handleMonthChange = (month: Date) => {
        setCurrentMonth(month)
    }

    // timeOptions removed as we're now using TimePicker component

    // Determine if we should include time based on the type prop or the deprecated includeTime prop
    const [shouldIncludeTime, setShouldIncludeTime] = useState(
        type === 'datetime' || includeTime,
    )

    // Update shouldIncludeTime when type or includeTime changes
    useEffect(() => {
        setShouldIncludeTime(type === 'datetime' || includeTime)
    }, [type, includeTime])

    // Detect mobile devices (below md breakpoint)
    const bp = useBreakpoints()

    const displayTimeFormat = shouldIncludeTime
        ? `${dateFormat} ${timeFormat}`
        : dateFormat

    // Guard against invalid dates
    const formatDateWithTime = (date: Date | undefined) => {
        if (!date) return ''
        const validDate = date instanceof Date ? date : new Date()
        return isNaN(validDate.getTime())
            ? ''
            : format(validDate, displayTimeFormat)
    }

    // Time picker implementation now uses the TimePicker component with mode option

    if (disabled) {
        // Use the provided value if available, otherwise use current date
        const displayDate = (date: Date | undefined) => {
            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
                return format(new Date(), dateFormat)
            }
            return format(
                date,
                shouldIncludeTime ? displayTimeFormat : dateFormat,
            )
        }

        // Format the date based on the mode and value
        let displayValue
        if (mode === 'range') {
            const range = dateValue as DateRange
            if (range?.from) {
                displayValue = range.to
                    ? `${displayDate(range.from)} - ${displayDate(range.to)}`
                    : displayDate(range.from)
            } else {
                const currentFormatted = format(new Date(), dateFormat)
                displayValue = `${currentFormatted} - ${currentFormatted}`
            }
        } else {
            displayValue =
                dateValue instanceof Date
                    ? displayDate(dateValue as Date)
                    : format(new Date(), dateFormat)
        }

        return (
            <div>
                {label && (
                    <Label
                        asChild
                        id="date"
                        position={labelPosition}
                        disabled={disabled}>
                        {label}
                    </Label>
                )}
                <div className="relative w-full">
                    <Button
                        id="date"
                        variant="outline"
                        disabled={disabled}
                        className={cn('px-4 justify-start w-full')}
                        iconLeft={renderIcon(
                            'mr-[8.5px] w-5 h-5 text-neutral-400',
                        )}
                        {...buttonProps}
                        type="button">
                        {displayValue}
                    </Button>
                </div>
            </div>
        )
    }

    const renderButtonLabel = () => {
        if (mode === 'range') {
            const range = dateValue as DateRange
            if (range?.from) {
                return range.to ? (
                    <>
                        {formatDateWithTime(range.from)} -{' '}
                        {formatDateWithTime(range.to)}
                    </>
                ) : (
                    formatDateWithTime(range.from)
                )
            }
            // Use consistent date format for placeholder
            return (
                <span className="text-muted-foreground">
                    {actualPlaceholder}
                </span>
            )
        } else if (dateValue) {
            return formatDateWithTime(dateValue as Date)
        }
        // Use consistent date format for placeholder
        return (
            <span className="text-muted-foreground">{actualPlaceholder}</span>
        )
    }

    // Shared content component for both Popover and Dialog
    const renderDatePickerContent = () => (
        <>
            {mode === 'range' ? (
                <>
                    <div className="flex flex-col justify-center items-center">
                        <Calendar
                            key="range"
                            autoFocus
                            mode="range"
                            month={currentMonth}
                            onMonthChange={handleMonthChange}
                            captionLayout="dropdown"
                            selected={
                                confirmSelection && pendingSelection
                                    ? (pendingSelection as DateRange)
                                    : (dateValue as DateRange)
                            }
                            onSelect={(value) => {
                                // Ensure we maintain the date format when a date is unselected
                                handleValueChange(value)
                            }}
                            numberOfMonths={numberOfMonths}
                            showWeekNumber={showWeekNumbers}
                            disabled={
                                validation
                                    ? (date) => !validateDate(date)
                                    : undefined
                            }
                        />
                        {shouldIncludeTime && (
                            <div className="p-3 border-t border-border">
                                <div className="mt-1">
                                    <TimePicker
                                        value={getTimePickerValue(
                                            (pendingSelection as DateRange)
                                                ?.from,
                                        )}
                                        toValue={getTimePickerValue(
                                            (pendingSelection as DateRange)?.to,
                                        )}
                                        onChange={handleTimeChange}
                                        onToChange={(date) =>
                                            handleTimeChange(date, true)
                                        }
                                        // Always enable time picker regardless of whether a date is selected
                                        disabled={false}
                                        className="w-full"
                                        mode={timeMode}
                                        label={
                                            timeMode === 'range'
                                                ? 'Time Range'
                                                : undefined
                                        }
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                    {confirmSelection && (
                        <>
                            <Separator className="my-0" />
                            <div className={cn('p-3 flex gap-3 justify-end')}>
                                {clearable && (
                                    <div className="w-fit">
                                        <Button
                                            variant="outline"
                                            onClick={handleClearInPopover}
                                            iconLeft={X}
                                            className="w-fit"
                                            aria-label="Clear date range">
                                            Clear
                                        </Button>
                                    </div>
                                )}
                                <Button
                                    onClick={handleConfirm}
                                    disabled={!pendingSelection}
                                    iconLeft={Check}>
                                    {confirmButtonText}
                                </Button>
                            </div>
                        </>
                    )}
                </>
            ) : (
                <div>
                    <div className="flex flex-col justify-center items-center">
                        <Calendar
                            key="single"
                            autoFocus
                            mode="single"
                            month={currentMonth}
                            onMonthChange={handleMonthChange}
                            selected={
                                confirmSelection && pendingSelection
                                    ? (pendingSelection as Date)
                                    : (dateValue as Date)
                            }
                            onSelect={(value) => {
                                // Ensure we maintain the date format when a date is unselected
                                handleValueChange(value)
                            }}
                            captionLayout="dropdown"
                            numberOfMonths={numberOfMonths}
                            showWeekNumber={showWeekNumbers}
                            disabled={
                                validation
                                    ? (date) => !validateDate(date)
                                    : undefined
                            }
                        />
                        {shouldIncludeTime && (
                            <div className="p-3 border-t border-border">
                                <div className="mt-1">
                                    <TimePicker
                                        value={getTimePickerValue(
                                            pendingSelection instanceof Date
                                                ? pendingSelection
                                                : undefined,
                                        )}
                                        toValue={getTimePickerValue(undefined)}
                                        onChange={handleTimeChange}
                                        onToChange={(date) =>
                                            handleTimeChange(date, true)
                                        }
                                        // Always enable time picker regardless of whether a date is selected
                                        disabled={false}
                                        className="w-full"
                                        mode={timeMode}
                                        label={
                                            timeMode === 'range'
                                                ? 'Time Range'
                                                : undefined
                                        }
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                    {confirmSelection && (
                        <>
                            <Separator className="my-0" />
                            <div
                                className={cn(
                                    'p-3 flex-1 flex gap-3 justify-end',
                                )}>
                                {clearable && (
                                    <div className="w-fit">
                                        <Button
                                            variant="outline"
                                            onClick={handleClearInPopover}
                                            iconLeft={X}
                                            aria-label="Clear date">
                                            Clear
                                        </Button>
                                    </div>
                                )}
                                <Button
                                    onClick={handleConfirm}
                                    disabled={!pendingSelection}
                                    className={cn(clearable ? '' : 'w-full')}>
                                    {confirmButtonText}
                                </Button>
                            </div>
                        </>
                    )}
                </div>
            )}
        </>
    )

    // Shared trigger button component
    const renderTriggerButton = () => (
        <div className="relative w-full">
            <Button
                id="date"
                variant="outline"
                disabled={disabled}
                iconLeft={renderIcon()}
                className={cn(
                    'px-4 justify-start w-full',
                    clearable && 'pr-10',
                )}
                {...buttonProps}
                type="button">
                {renderButtonLabel()}
            </Button>
            {clearable &&
                (dateValue instanceof Date ||
                    (dateValue as DateRange)?.from) && (
                    <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                        onClick={handleClear}
                        aria-label="Clear date">
                        <X
                            size={16}
                            className="text-neutral-400 hover:text-background0"
                        />
                    </Button>
                )}
        </div>
    )

    const handleOpenChange = (isOpen: boolean) => {
        // When opening, initialize pendingSelection with the current value
        if (isOpen && !pendingSelection && dateValue) {
            setPendingSelection(dateValue)
        }

        // When opening, ensure currentMonth shows the correct month
        if (isOpen) {
            if (dateValue instanceof Date) {
                setCurrentMonth(dateValue)
            } else if (dateValue && 'from' in dateValue && dateValue.from) {
                setCurrentMonth(dateValue.from)
            }
        }

        setOpen(isOpen)
    }

    return (
        <div className={cn(wrapperClassName)}>
            {label && (
                <Label id="date" position={labelPosition} disabled={disabled}>
                    {label}
                </Label>
            )}
            {!bp.phablet ? (
                // Mobile: Use Dialog
                <Dialog open={open} onOpenChange={handleOpenChange}>
                    <DialogTrigger asChild>
                        {renderTriggerButton()}
                    </DialogTrigger>
                    <DialogContent className="gap-0 p-0 tiny:w-fit h-svh max-h-svh tiny:h-auto flex flex-col overflow-auto rounded-md">
                        <div className="h-10 px-2.5 flex items-center justify-start">
                            <P>{renderButtonLabel()}</P>
                        </div>
                        {renderDatePickerContent()}
                    </DialogContent>
                </Dialog>
            ) : (
                // Desktop: Use Popover
                <Popover
                    modal={modal}
                    open={open}
                    onOpenChange={handleOpenChange}>
                    <PopoverTrigger asChild>
                        {renderTriggerButton()}
                    </PopoverTrigger>
                    <PopoverContent
                        side="bottom"
                        sideOffset={4}
                        collisionPadding={16}
                        className="w-auto overflow-y-auto max-h-[58svh] rounded-md p-0"
                        align="start">
                        {renderDatePickerContent()}
                    </PopoverContent>
                </Popover>
            )}
        </div>
    )
}

export default DatePicker
